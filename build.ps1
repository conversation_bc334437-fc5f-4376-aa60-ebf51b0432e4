# PowerShell 构建脚本 - 替代 build.sh
Set-Location src

Write-Host "开始构建 macOS ARM64 版本..." -ForegroundColor Green
$env:target="darwin"
npx electron-builder build --macos --arm64

Write-Host "开始构建 macOS x64 版本..." -ForegroundColor Green  
$env:target="darwin"
npx electron-builder build --macos --x64

Write-Host "开始构建 Windows x64 版本..." -ForegroundColor Green
$env:target="win32" 
npx electron-builder build --win --x64

Write-Host "构建完成！输出目录：src/build/" -ForegroundColor Yellow
